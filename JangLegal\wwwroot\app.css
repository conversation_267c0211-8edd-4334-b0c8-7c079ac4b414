@import '/_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css';

body {
    --body-font: "Segoe UI Variable", "Segoe UI", sans-serif;
    font-family: var(--body-font);
    font-size: var(--type-ramp-base-font-size);
    line-height: var(--type-ramp-base-line-height);
    margin: 0;
}

.navmenu-icon {
    display: none;
}

.main {
    min-height: calc(100dvh - 86px);
    color: var(--neutral-foreground-rest);
    align-items: stretch !important;
}

.body-content {
    align-self: stretch;
    height: unset !important;
    display: flex;
}

.content {
    padding: 0.5rem 1.5rem;
    align-self: stretch !important;
    width: 100%;
}

.manage {
    width: 100dvw;
}

footer {
    display: grid;
    grid-template-columns: 10px auto auto 10px;
    background: var(--neutral-layer-4);
    color: var(--neutral-foreground-rest);
    align-items: center;
    padding: 10px 10px;
}

    footer .link1 {
        grid-column: 2;
        justify-content: start;
    }

    footer .link2 {
        grid-column: 3;
        justify-self: end;
    }

    footer a {
        color: var(--neutral-foreground-rest);
        text-decoration: none;
    }

        footer a:focus {
            outline: 1px dashed;
            outline-offset: 3px;
        }

        footer a:hover {
            text-decoration: underline;
        }

.alert {
    border: 1px dashed var(--accent-fill-rest);
    padding: 5px;
}


#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
    margin: 20px 0;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::before {
        content: "An error has occurred. "
    }

.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

    .loading-progress circle {
        fill: none;
        stroke: #e0e0e0;
        stroke-width: 0.6rem;
        transform-origin: 50% 50%;
        transform: rotate(-90deg);
    }

        .loading-progress circle:last-child {
            stroke: #1b6ec2;
            stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
            transition: stroke-dasharray 0.05s ease-in-out;
        }

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

    .loading-progress-text:after {
        content: var(--blazor-load-percentage-text, "Loading");
    }

code {
    color: #c02d76;
}

@media (max-width: 600px) {
    .main {
        flex-direction: column !important;
        row-gap: 0 !important;
    }

    nav.sitenav {
        width: 100%;
        height: 100%;
    }

    #main-menu {
        width: 100% !important;
    }

        #main-menu > div:first-child:is(.expander) {
            display: none;
        }

    .navmenu {
        width: 100%;
    }

    #navmenu-toggle {
        appearance: none;
    }

        #navmenu-toggle ~ nav {
            display: none;
        }

        #navmenu-toggle:checked ~ nav {
            display: block;
        }

    .navmenu-icon {
        cursor: pointer;
        z-index: 10;
        display: block;
        position: absolute;
        top: 15px;
        right: 20px;
        width: 20px;
        height: 20px;
        border: none;
    }
}


.loginForm{
    width: 500px;
    margin: auto auto;
    margin-top: 10vh;
    background: #eee;
    padding: 20px;
    border-radius: 12px;
              border: 1px solid gray;
              box-shadow: 2px 2px 5px rgba(0,0,0,0.3);
          }

.loginForm input {
  width: 100%;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
  border: 1px solid #ccc;
  font-size: 16px;
}

.loginForm .loginBtn {
  background-color: #4caf50;
  border: none;
  color: white;
  padding: 15px 32px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 8px;
}


@media (max-width: 600px) {
    .loginForm {
      width: 90%;
      margin: 20px auto;
      padding: 10px;
    }
  
    .loginForm input {
      padding: 10px;
      font-size: 18px;
    }
  
    .loginForm .loginBtn {
      padding: 10px 20px;
      font-size: 18px;
    }
  }

/* Card Styles */
.card {
    background: var(--neutral-layer-1);
    border-radius: 8px;
    border: 1px solid var(--neutral-stroke-rest);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.2s ease-in-out;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 1.25rem;
}

.card-header {
    padding: 1rem 1.25rem;
    background: var(--neutral-layer-2);
    border-bottom: 1px solid var(--neutral-stroke-rest);
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-footer {
    padding: 1rem 1.25rem;
    background: var(--neutral-layer-2);
    border-top: 1px solid var(--neutral-stroke-rest);
    border-radius: 0 0 8px 8px;
}

/* Card Title Styles */
.card-title {
    margin: 0;
    font-size: var(--type-ramp-plus-1-font-size);
    line-height: var(--type-ramp-plus-1-line-height);
    font-weight: 600;
}

.card-subtitle {
    margin: 0.25rem 0 0;
    font-size: var(--type-ramp-base-font-size);
    color: var(--neutral-foreground-hint);
}

/* Card Content Styles */
.card-text {
    margin-bottom: 1rem;
    color: var(--neutral-foreground-rest);
}

.card-text:last-child {
    margin-bottom: 0;
}

/* Card Groups */
.card-group {
    display: grid;
    gap: 1rem;
    margin: 1rem 0;
}

@media (min-width: 768px) {
    .card-group {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}
  
.gap-2
{
    gap: 0.5rem;
}

.user-welcome {
  display: inline-block;
}

@media (max-width: 600px) {
  .user-welcome {
    display: none;
  }
}

/* Proceeding Styles */
.proceedings-timeline {
    position: relative;
}

.proceeding-card {
    background: var(--neutral-layer-1);
    border: 1px solid var(--neutral-stroke-rest);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.2s ease-in-out;
    position: relative;
    margin-left: 20px;
}

.proceeding-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.proceeding-card::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 20px;
    width: 12px;
    height: 12px;
    background: var(--accent-fill-rest);
    border-radius: 50%;
    border: 3px solid var(--neutral-layer-1);
}

.proceeding-card:not(:last-child)::after {
    content: '';
    position: absolute;
    left: -15px;
    top: 32px;
    width: 2px;
    height: calc(100% - 12px);
    background: var(--neutral-stroke-rest);
}

.proceeding-header {
    padding: 1rem 1.25rem;
    background: var(--neutral-layer-2);
    border-bottom: 1px solid var(--neutral-stroke-rest);
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.proceeding-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--neutral-foreground-rest);
}

.proceeding-date .material-icons {
    font-size: 18px;
    color: var(--accent-fill-rest);
}

.proceeding-date .time {
    font-weight: 400;
    color: var(--neutral-foreground-hint);
    margin-left: 0.5rem;
}

.proceeding-actions {
    display: flex;
    gap: 0.5rem;
}

.proceeding-details {
    padding: 1.25rem;
}

.detail-item {
    margin-bottom: 0.75rem;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-item label {
    font-weight: 600;
    color: var(--neutral-foreground-hint);
    display: block;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.detail-item span {
    color: var(--neutral-foreground-rest);
}

.detail-item .remarks {
    background: var(--neutral-layer-2);
    padding: 0.75rem;
    border-radius: 4px;
    border: 1px solid var(--neutral-stroke-rest);
    margin-top: 0.25rem;
    white-space: pre-wrap;
}

.proceeding-summary {
    border-top: 1px solid var(--neutral-stroke-rest);
    padding-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.summary-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--neutral-foreground-hint);
}

.stat-item .material-icons {
    font-size: 16px;
    color: var(--accent-fill-rest);
}

.summary-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* File Upload Styles */
.file-upload-container {
    position: relative;
    display: inline-block;
    width: 100%;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px dashed var(--neutral-stroke-rest);
    border-radius: 8px;
    background: var(--neutral-layer-2);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    color: var(--neutral-foreground-hint);
}

.file-upload-label:hover {
    border-color: var(--accent-fill-rest);
    background: var(--neutral-layer-3);
    color: var(--neutral-foreground-rest);
}

.file-upload-label .material-icons {
    font-size: 24px;
    color: var(--accent-fill-rest);
}

.file-preview {
    margin-top: 1rem;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--neutral-layer-2);
    border: 1px solid var(--neutral-stroke-rest);
    border-radius: 6px;
}

.file-info .material-icons {
    font-size: 24px;
    color: var(--accent-fill-rest);
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--neutral-foreground-rest);
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.875rem;
    color: var(--neutral-foreground-hint);
}

.btn-remove {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--neutral-foreground-hint);
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
}

.btn-remove:hover {
    background: var(--neutral-fill-stealth-hover);
    color: var(--error-fill-rest);
}

.btn-remove .material-icons {
    font-size: 18px;
}

.file-requirements {
    margin-top: 0.5rem;
}

.file-requirements .material-icons {
    font-size: 16px;
    vertical-align: middle;
    margin-right: 0.25rem;
}

/* Responsive Design for Proceedings */
@media (max-width: 768px) {
    .proceeding-card {
        margin-left: 0;
    }

    .proceeding-card::before,
    .proceeding-card::after {
        display: none;
    }

    .proceeding-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .proceeding-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .proceeding-summary {
        flex-direction: column;
        align-items: flex-start;
    }

    .summary-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

