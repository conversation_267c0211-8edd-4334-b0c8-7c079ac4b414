using System.ComponentModel.DataAnnotations;

namespace JangLegal.DTO;

public class CaseProceedingDto
{
    public int ProceedingId { get; set; }
    
    [Required(ErrorMessage = "Court Case ID is required")]
    public int CourtCaseId { get; set; }
    
    [Required(ErrorMessage = "Proceeding date is required")]
    public DateTime ProceedingDate { get; set; }
    
    
    
    [StringLength(255, ErrorMessage = "Presiding judge name cannot be longer than 255 characters")]
    public string PresidingJudge { get; set; } = string.Empty;
    
    [StringLength(50, ErrorMessage = "Court room number cannot be longer than 50 characters")]
    public string CourtRoomNumber { get; set; } = string.Empty;
    
    public DateTime? NextProceedingDate { get; set; }
    
    
    
    [StringLength(1000, ErrorMessage = "Remarks cannot be longer than 1000 characters")]
    public string Remarks { get; set; } = string.Empty;
    
    public DateTime? CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
    
    public int? ProceedingTypeId { get; set; }
    public string ProceedingTypeName { get; set; } = string.Empty;
    
    public int? ProceedingStatusId { get; set; }
    public string ProceedingStatusName { get; set; } = string.Empty;
    
    // Navigation properties as DTOs
    public List<ProceedingOutcomeDto> ProceedingOutcomes { get; set; } = new List<ProceedingOutcomeDto>();
    public List<ProceedingAttendeeDto> ProceedingAttendees { get; set; } = new List<ProceedingAttendeeDto>();
    public List<ProceedingDocumentDto> ProceedingDocuments { get; set; } = new List<ProceedingDocumentDto>();
    
    // Computed properties for display
    public string ProceedingDateFormatted => ProceedingDate.ToString("dd MMM, yyyy");
    //public string ProceedingTimeFormatted => ProceedingTime?.ToString("HH:mm") ?? "";
    public string NextProceedingDateFormatted => NextProceedingDate?.ToString("dd MMM, yyyy") ?? "";
    //public string NextProceedingTimeFormatted => NextProceedingTime?.ToString("HH:mm") ?? "";
    public int OutcomeCount => ProceedingOutcomes?.Count ?? 0;
    public int AttendeeCount => ProceedingAttendees?.Count ?? 0;
    public int DocumentCount => ProceedingDocuments?.Count ?? 0;
}
