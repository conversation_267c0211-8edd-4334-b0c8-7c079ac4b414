@page "/court-cases/new"
@page "/court-cases/edit/{Id:int}"
@rendermode InteractiveServer
@inject CourtCaseService CaseService
@inject PlaintiffTypeService PlaintiffTypeService
@inject NavigationManager NavMgr


<div class="d-flex justify-content-between align-items-center mb-3">
    <h3>@(Id == null ? "New Court Case" : "Edit Court Case")</h3>
    <SfButton OnClick="@(() => NavMgr.NavigateTo("/court-cases", true))" CssClass="e-outline">Back to List</SfButton>
</div>
<SfToast @ref="ToastObj" />
<EditForm Model="CaseDto" OnValidSubmit="SaveCase" FormName="court_case_form">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="card mb-3">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <SfTextBox @bind-Value="CaseDto.CaseNumber"
                               Placeholder="Case Number"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                    <ValidationMessage For="@(() => CaseDto.CaseNumber)" />
                </div>

                <div class="col-md-8">
                    <SfTextBox @bind-Value="CaseDto.Title"
                               Placeholder="Case Title"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                </div>

                <div class="col-md-4">
                    <SfDropDownList TValue="int" TItem="CourtDto"
                                    @bind-Value="CaseDto.CourtId"
                                    DataSource="@Courts"
                                    AllowFiltering="true"
                                    ShowClearButton="true"
                                    FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                    Placeholder="Select Court"
                                    FloatLabelType="FloatLabelType.Always">
                        <DropDownListFieldSettings Text="Name" Value="Id" />
                    </SfDropDownList>
                    <ValidationMessage For="@(() => CaseDto.CourtId)" />
                </div>

                <div class="col-md-4">
                    <SfNumericTextBox TValue="int?" @bind-Value="CaseDto.CaseFilingYear"
                                      Placeholder="Filing Year"
                                      FloatLabelType="FloatLabelType.Always">
                    </SfNumericTextBox>
                </div>

                <div class="col-md-4">
                    <SfDropDownList TValue="int?" TItem="CaseCategoryDto"
                                    @bind-Value="CaseDto.CaseCategoryId"
                                    DataSource="@Categories"
                                    Placeholder="Select Category"
                                    AllowFiltering="true"
                                    ShowClearButton="true"
                                    FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                    FloatLabelType="FloatLabelType.Always">
                        <DropDownListFieldSettings Text="Title" Value="Id" />
                    </SfDropDownList>
                    <ValidationMessage For="@(() => CaseDto.CaseCategoryId)" />
                </div>

                <div class="col-md-4">
                    <SfDatePicker TValue="DateTime?" @bind-Value="CaseDto.DateInOffice"
                                  Placeholder="Date in Office"
                                  FloatLabelType="FloatLabelType.Always">
                    </SfDatePicker>
                </div>

                <div class="col-md-4">
                    <SfNumericTextBox TValue="decimal?" @bind-Value="CaseDto.ClaimAmount"
                                      Format="c2"
                                      Placeholder="Claim Amount"
                                      FloatLabelType="FloatLabelType.Always">
                    </SfNumericTextBox>
                </div>

                <div class="col-md-4">
                    <SfDropDownList TValue="int?" TItem="CaseNatureDto"
                                    @bind-Value="CaseDto.CaseNatureId"
                                    DataSource="@CaseNatures"
                                    Placeholder="Select Case Nature"
                                    FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true"
                                    ShowClearButton="true"
                                    FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                        <DropDownListFieldSettings Text="Name" Value="Id" />
                    </SfDropDownList>
                </div>
                <div class="col-md-">
                    <div>Decided</div>
                    <SfSwitch OnLabel="Yes" OffLabel="No" @bind-Checked="CaseDto.IsDecided"></SfSwitch>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-body">
            <h5>Case Pray</h5>
            
            <Syncfusion.Blazor.RichTextEditor.SfRichTextEditor @bind-Value="CaseDto.Pray" >

            </Syncfusion.Blazor.RichTextEditor.SfRichTextEditor>
            
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-body">
            <h5>Case Synopsis</h5>
            <Syncfusion.Blazor.RichTextEditor.SfRichTextEditor @bind-Value="CaseDto.CaseSynopsis">

            </Syncfusion.Blazor.RichTextEditor.SfRichTextEditor>
        </div>
    </div>


    <!-- Plaintiffs Section -->
    <div class="card mb-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>Plaintiffs</h5>
                <SfButton type="button" OnClick="OpenPlaintiffForm" CssClass="e-primary">Add Plaintiff</SfButton>
            </div>
            <ValidationMessage For="@(() => CaseDto.Plaintiffs)" />

            @if (CaseDto.Plaintiffs?.Any() == true)
            {
                <SfGrid DataSource="CaseDto.Plaintiffs" AllowPaging="true" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn Field="@nameof(PlaintiffDto.PlaintiffType)" HeaderText="Type" AutoFit="true"></GridColumn>
                        <GridColumn HeaderText="Plaintiff" Width="250px">
                            <Template Context="gridContext">
                                @{
                                    if (gridContext is PlaintiffDto plaintiff)
                                    {
                                        @if (plaintiff.PlaintiffTypeId == 1)
                                        {
                                            @plaintiff.EmployeeName
                                        }
                                        else if (plaintiff.PlaintiffTypeId == 2)
                                        {
                                            @plaintiff.CouncilOfGroupCompanyName
                                        }
                                        else if (plaintiff.PlaintiffTypeId == 3)
                                        {
                                            @plaintiff.CouncilOfNonGroupCompanyName
                                        }
                                        else if (plaintiff.PlaintiffTypeId == 4)
                                        {
                                            @plaintiff.OtherPlaintiff
                                        }
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Field="@nameof(PlaintiffDto.LawFirmName)" HeaderText="Law Firm" Width="250px"></GridColumn>
                        <GridColumn Field="@nameof(PlaintiffDto.LawyerName)" HeaderText="Lawyer" Width="250px"></GridColumn>
                        <GridColumn HeaderText="Actions" AutoFit="true">
                            <Template Context="gridContext">
                                @{
                                    if (gridContext is PlaintiffDto plaintiff)
                                    {
                                        <SfButton OnClick="@(() => EditPlaintiff(plaintiff))"
                                                  type="button"
                                                  CssClass="e-primary e-small">Edit</SfButton>
                                        <SfButton OnClick="@(() => RemovePlaintiff(plaintiff.Id))"
                                                  type="button"
                                                  CssClass="e-danger e-small">Remove</SfButton>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </div>
    </div>

    <!-- Respondents Section -->
    <div class="card mb-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>Respondents</h5>
                <SfButton type="button" OnClick="@(() => { _selectedRespondent = new RespondentDto(); _isRespondentDialogOpen = true; })" CssClass="e-primary">Add Respondent</SfButton>
            </div>

            @if (CaseDto.Respondents?.Any() == true)
            {
                <SfGrid DataSource="CaseDto.Respondents" AllowPaging="true" AllowSorting="true" AllowTextWrap="true"
                        AllowFiltering="true">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn Field="@nameof(RespondentDto.Name)" HeaderText="Name" Width="250px"></GridColumn>
                        <GridColumn Field="@nameof(RespondentDto.LawFirmName)" HeaderText="Law Firm" Width="250px"></GridColumn>
                        <GridColumn Field="@nameof(RespondentDto.LawyerName)" HeaderText="Lawyer" Width="250px"></GridColumn>
                        <GridColumn HeaderText="Actions" AutoFit="true">
                            <Template Context="gridContext">
                                @{
                                    if (gridContext is RespondentDto respondent)
                                    {
                                        <SfButton OnClick="@(() => EditRespondent(respondent))"
                                                  type="button"
                                                  CssClass="e-primary e-small">Edit</SfButton>
                                        <SfButton OnClick="@(() => RemoveRespondent(respondent))"
                                                  type="button"
                                                  CssClass="e-danger e-small">Remove</SfButton>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </div>
    </div>

    <!-- Attachments Section - Only visible when editing -->
    @if (Id.HasValue)
    {
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Attachments</h5>
                    <SfButton type="button" OnClick="OpenAttachmentDialog" CssClass="e-primary">Add Attachment</SfButton>
                </div>

                @if (CaseDto.Attachments?.Any() == true)
                {
                    <SfGrid DataSource="CaseDto.Attachments" AllowPaging="true" AllowSorting="true" AllowTextWrap="true"
                            AllowFiltering="true">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn Field="@nameof(AttachmentDto.FileName)" HeaderText="File Name" Width="250px"></GridColumn>
                            <GridColumn Field="@nameof(AttachmentDto.AttachmentTypeName)" HeaderText="Type" Width="150px"></GridColumn>
                            <GridColumn Field="@nameof(AttachmentDto.CreatedDate)" HeaderText="Upload Date" Width="150px" Format="d"></GridColumn>
                            <GridColumn Field="@nameof(AttachmentDto.CreatedBy)" HeaderText="Uploaded By" Width="150px"></GridColumn>
                            <GridColumn HeaderText="Actions" Width="200px">
                                <Template Context="gridContext">
                                    @{
                                        if (gridContext is AttachmentDto attachment)
                                        {
                                            <SfButton OnClick="@(() => DownloadAttachment(attachment))"
                                                      type="button"
                                                      CssClass="e-info e-small">Download</SfButton>
                                            <SfButton OnClick="@(() => DeleteAttachment(attachment))"
                                                      type="button"
                                                      CssClass="e-danger e-small">Delete</SfButton>
                                        }
                                    }
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                }
                else
                {
                    <p class="text-muted">No attachments uploaded yet.</p>
                }
            </div>
        </div>
    }

    <!-- Case Proceedings Section - Only visible when editing -->
    @if (Id.HasValue)
    {
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Case Proceedings</h5>
                    <SfButton type="button" OnClick="OpenProceedingDialog" CssClass="e-primary">Add Proceeding</SfButton>
                </div>

                @if (CaseDto.CaseProceedings?.Any() == true)
                {
                    <div class="proceedings-timeline">
                        @foreach (var proceeding in CaseDto.CaseProceedings.OrderBy(p => p.ProceedingDate))
                        {
                            <div class="proceeding-card mb-4">
                                <div class="proceeding-header">
                                    <div class="proceeding-date">
                                        <i class="material-icons">event</i>
                                        <span>@proceeding.ProceedingDateFormatted</span>
                                        @if (!string.IsNullOrEmpty(proceeding.ProceedingTimeFormatted))
                                        {
                                            <span class="time">@proceeding.ProceedingTimeFormatted</span>
                                        }
                                    </div>
                                    <div class="proceeding-actions">
                                        <SfButton OnClick="@(() => EditProceeding(proceeding))"
                                                  type="button"
                                                  CssClass="e-info e-small">Edit</SfButton>
                                        <SfButton OnClick="@(() => DeleteProceeding(proceeding.ProceedingId))"
                                                  type="button"
                                                  CssClass="e-danger e-small">Delete</SfButton>
                                    </div>
                                </div>

                                <div class="proceeding-details">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="detail-item">
                                                <label>Proceeding Type:</label>
                                                <span>@proceeding.ProceedingTypeName</span>
                                            </div>
                                            <div class="detail-item">
                                                <label>Presiding Judge:</label>
                                                <span>@proceeding.PresidingJudge</span>
                                            </div>
                                            <div class="detail-item">
                                                <label>Court Room:</label>
                                                <span>@proceeding.CourtRoomNumber</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="detail-item">
                                                <label>Status:</label>
                                                <span>@proceeding.ProceedingStatusName</span>
                                            </div>
                                            @if (proceeding.NextProceedingDate.HasValue)
                                            {
                                                <div class="detail-item">
                                                    <label>Next Proceeding:</label>
                                                    <span>@proceeding.NextProceedingDateFormatted @proceeding.NextProceedingTimeFormatted</span>
                                                </div>
                                            }
                                        </div>
                                    </div>

                                    @if (!string.IsNullOrEmpty(proceeding.Remarks))
                                    {
                                        <div class="detail-item">
                                            <label>Remarks:</label>
                                            <div class="remarks">@proceeding.Remarks</div>
                                        </div>
                                    }

                                    <!-- Proceeding Summary -->
                                    <div class="proceeding-summary mt-3">
                                        <div class="summary-stats">
                                            <span class="stat-item">
                                                <i class="material-icons">assignment</i>
                                                @proceeding.OutcomeCount Outcomes
                                            </span>
                                            <span class="stat-item">
                                                <i class="material-icons">people</i>
                                                @proceeding.AttendeeCount Attendees
                                            </span>
                                            <span class="stat-item">
                                                <i class="material-icons">description</i>
                                                @proceeding.DocumentCount Documents
                                            </span>
                                        </div>
                                        <div class="summary-actions">
                                            <SfButton OnClick="@(() => ManageOutcomes(proceeding.ProceedingId))"
                                                      type="button"
                                                      CssClass="e-outline e-small">Manage Outcomes</SfButton>
                                            <SfButton OnClick="@(() => ManageAttendees(proceeding.ProceedingId))"
                                                      type="button"
                                                      CssClass="e-outline e-small">Manage Attendees</SfButton>
                                            <SfButton OnClick="@(() => ManageDocuments(proceeding.ProceedingId))"
                                                      type="button"
                                                      CssClass="e-outline e-small">Manage Documents</SfButton>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <p class="text-muted">No proceedings recorded yet.</p>
                }
            </div>
        </div>
    }

    <div class="d-flex gap-2 justify-content-end">
        <SfButton Type="submit" CssClass="e-primary">Save Case</SfButton>
        <SfButton OnClick="@(() => NavMgr.NavigateTo("/court-cases", true))" type="button"
                  CssClass="e-outline">Back to Cases</SfButton>
    </div>
</EditForm>


<!-- Verdict Selector Dialog -->
<SfDialog @bind-Visible="_isVerdictSelectorOpen" Width="800px" IsModal="true">
    <DialogTemplates>
        <Header>Select Verdict</Header>
        <Content>
            <SfGrid DataSource="Verdicts" AllowPaging="true">
                <GridColumns>
                    <GridColumn Field="@nameof(CaseVerdictDto.Title)" HeaderText="Title"></GridColumn>
                    <GridColumn Field="@nameof(CaseVerdictDto.Description)" HeaderText="Description"></GridColumn>
                    <GridColumn HeaderText="Actions" Width="100">
                        <Template>
                            @{
                                if (context is CaseVerdictDto verdict)
                                {
                                    <SfButton OnClick="@(() => SelectVerdict(verdict))"
                                              type="button"
                                              CssClass="e-primary">Select</SfButton>
                                }
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Plaintiff Form Dialog -->
<SfDialog @bind-Visible="_isPlaintiffDialogOpen" Width="600px" IsModal="true">
    <DialogTemplates>
        <Header>Plaintiff Detail</Header>
        <Content>
            <EditForm Model="_selectedPlaintiff" OnValidSubmit="SavePlaintiff">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="mb-3">
                    <SfDropDownList TItem="PlaintiffTypeDto" TValue="int"
                                    @bind-Value="_selectedPlaintiff.PlaintiffTypeId"
                                    DataSource="@_plaintiffTypes"
                                    Placeholder="Select Plaintiff Type"
                                    FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true"
                                    FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                        <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="int" TItem="PlaintiffTypeDto"
                                            ValueChange="@(e => OnPlaintiffTypeChanged(e))">
                        </DropDownListEvents>
                    </SfDropDownList>
                </div>

                @if (_selectedPlaintiff.PlaintiffTypeId == 1)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="CaseEmployeeDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.CaseEmployeeId"
                                        DataSource="@_employees"
                                        Placeholder="Select Employee"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="Name" Value="Id"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="CaseEmployeeDto"
                                                ValueChange="@(e => OnEmployeeSelectionChanged(e))">
                            </DropDownListEvents>
                        </SfDropDownList>
                    </div>

                    <div class="mb-3">
                        <SfTextBox @bind-Value="_selectedPlaintiff.EmployeeName"
                                   Placeholder="Employee Name"
                                   Enabled="false"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>

                    <div class="mb-3">
                        <SfTextBox @bind-Value="_selectedPlaintiff.EmployeeCode"
                                   Placeholder="Employee Code"
                                   Enabled="false"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId == 2)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="CouncilOfGroupCompanyDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.CouncilOfGroupCompanyId"
                                        DataSource="@_groupCompanyCouncils"
                                        Placeholder="Select Group Company Council"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId == 3)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="CouncilOfNonGroupCompanyDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.CouncilOfNonGroupCompanyId"
                                        DataSource="@_nonGroupCompanyCouncils"
                                        Placeholder="Select Non-Group Company Council"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId == 4)
                {
                    <div class="mb-3">
                        <SfTextBox @bind-Value="_selectedPlaintiff.OtherPlaintiff"
                                   Placeholder="Other Plaintiff"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId != 4)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="LawFirmDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.LawFirmId"
                                        DataSource="@_lawFirms"
                                        Placeholder="Select Law Firm"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="LawFirmDto"
                                                ValueChange="@(e => OnLawFirmChanged(e))">
                            </DropDownListEvents>
                        </SfDropDownList>
                    </div>

                    <div class="mb-3">
                        <SfDropDownList TItem="LawyerDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.LawyerId"
                                        DataSource="@FilteredPlaintiffLawyers"
                                        Placeholder="Select Lawyer"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        Enabled="@(_selectedPlaintiff.LawFirmId.HasValue)"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="Name" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                }


                <div class="d-flex gap-2 justify-content-end">
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                    <SfButton OnClick="@(() => _isPlaintiffDialogOpen = false)" type="button"
                              CssClass="e-outline">Cancel</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Respondent Form Dialog -->
<SfDialog Width="800px" IsModal="true" ShowCloseIcon="true" @bind-Visible="_isRespondentDialogOpen">
    <DialogTemplates>
        <Header>Respondent Detail</Header>
        <Content>
            <EditForm Model="_selectedRespondent" OnValidSubmit="SaveRespondent">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <SfTextBox @bind-Value="_selectedRespondent.Name"
                                   Placeholder="Name"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                        <ValidationMessage For="@(() => _selectedRespondent.Name)" />
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TValue="int?" TItem="LawFirmDto"
                                        @bind-Value="_selectedRespondent.LawFirmId"
                                        DataSource="@LawFirms"
                                        Placeholder="Select Law Firm"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="Title" Value="Id" />
                            @*<DropDownListEvents TValue="int?" TItem="LawFirmDto"
                                                 ValueChange="@OnLawFirmChange">
                                </DropDownListEvents>*@
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TValue="int?" TItem="LawyerDto"
                                        @bind-Value="_selectedRespondent.LawyerId"
                                        DataSource="@FilteredLawyers"
                                        Placeholder="Select Lawyer"
                                        FloatLabelType="FloatLabelType.Always"
                                        Enabled="@(_selectedRespondent.LawFirmId.HasValue)">
                            <DropDownListFieldSettings Text="Name" Value="Id" />
                        </SfDropDownList>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => _isRespondentDialogOpen = false)" type="button"
                              CssClass="e-outline-secondary">Cancel</SfButton>
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Attachment Upload Dialog -->
<SfDialog @bind-Visible="_isAttachmentDialogOpen" Width="600px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>Add Attachment</Header>
        <Content>
            <EditForm Model="_selectedAttachment" OnValidSubmit="SaveAttachment">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="mb-3">
                    <label class="form-label">Attachment Type</label>
                    <SfDropDownList TItem="AttachmentTypeDto" TValue="int?"
                                    @bind-Value="_selectedAttachment.AttachmentTypeId"
                                    DataSource="@_attachmentTypes"
                                    Placeholder="Select Attachment Type"
                                    FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true"
                                    FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                        <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                    </SfDropDownList>
                    <ValidationMessage For="@(() => _selectedAttachment.AttachmentTypeId)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Custom File Name (Optional)</label>
                    <SfTextBox @bind-Value="_customFileName"
                               Placeholder="Enter custom file name (leave empty to use original name)"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                    <small class="text-muted">If provided, this name will be used instead of the original file name</small>
                </div>

                <div class="mb-3">
                    <label class="form-label">Select File</label>
                    <div class="file-upload-container">
                        <InputFile OnChange="OnFileSelected"
                                   class="file-input"
                                   id="fileInput"
                                   accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.bmp" />
                        <label for="fileInput" class="file-upload-label">
                            <i class="material-icons">cloud_upload</i>
                            <span>Choose File</span>
                        </label>
                    </div>

                    @if (!string.IsNullOrEmpty(_fileValidationMessage))
                    {
                        <div class="text-danger mt-2">
                            <i class="material-icons">error</i>
                            @_fileValidationMessage
                        </div>
                    }

                    @if (_selectedFile != null)
                    {
                        <div class="file-preview mt-3">
                            <div class="file-info">
                                <i class="material-icons">description</i>
                                <div class="file-details">
                                    <div class="file-name">@_selectedFile.Name</div>
                                    <div class="file-size">@FormatFileSize(_selectedFile.Size)</div>
                                </div>
                                <button type="button" class="btn-remove" @onclick="ClearSelectedFile">
                                    <i class="material-icons">close</i>
                                </button>
                            </div>
                        </div>
                    }

                    <div class="file-requirements mt-2">
                        <small class="text-muted">
                            <i class="material-icons">info</i>
                            Allowed: PDF, Word, Excel, Images • Max size: 10MB
                        </small>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => _isAttachmentDialogOpen = false)" type="button"
                              CssClass="e-outline-secondary">Cancel</SfButton>
                    <SfButton Type="submit" CssClass="e-primary" Disabled="@(_selectedFile == null || _selectedAttachment.AttachmentTypeId == null)">Upload</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Proceeding Form Dialog -->
<SfDialog @bind-Visible="_isProceedingDialogOpen" Width="800px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>@(_selectedProceeding.ProceedingId == 0 ? "Add Proceeding" : "Edit Proceeding")</Header>
        <Content>
            <EditForm Model="_selectedProceeding" OnValidSubmit="SaveProceeding">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDatePicker TValue="DateTime" @bind-Value="_selectedProceeding.ProceedingDate"
                                      Placeholder="Proceeding Date"
                                      FloatLabelType="FloatLabelType.Always">
                        </SfDatePicker>
                        <ValidationMessage For="@(() => _selectedProceeding.ProceedingDate)" />
                    </div>
                    
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TItem="ProceedingTypeDto" TValue="int?"
                                        @bind-Value="_selectedProceeding.ProceedingTypeId"
                                        DataSource="@_proceedingTypes"
                                        Placeholder="Select Proceeding Type"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="TypeName" Value="ProceedingTypeId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TItem="ProceedingStatusDto" TValue="int?"
                                        @bind-Value="_selectedProceeding.ProceedingStatusId"
                                        DataSource="@_proceedingStatuses"
                                        Placeholder="Select Proceeding Status"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="StatusName" Value="ProceedingStatusId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfTextBox @bind-Value="_selectedProceeding.PresidingJudge"
                                   Placeholder="Presiding Judge"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfTextBox @bind-Value="_selectedProceeding.CourtRoomNumber"
                                   Placeholder="Court Room Number"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDatePicker TValue="DateOnly?" @bind-Value="_selectedProceeding.NextProceedingDate"
                                      Placeholder="Next Proceeding Date"
                                      FloatLabelType="FloatLabelType.Always">
                        </SfDatePicker>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfTimePicker TValue="TimeOnly?" @bind-Value="_selectedProceeding.NextProceedingTime"
                                      Placeholder="Next Proceeding Time"
                                      FloatLabelType="FloatLabelType.Always">
                        </SfTimePicker>
                    </div>
                </div>

                <div class="mb-3">
                    <SfTextBox @bind-Value="_selectedProceeding.Remarks"
                               Placeholder="Remarks"
                               Multiline="true"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => _isProceedingDialogOpen = false)" type="button"
                              CssClass="e-outline-secondary">Cancel</SfButton>
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code
{
    [Parameter] public int? Id { get; set; }
    //[Inject] private CourtCaseService CaseService { get; set; } = default!;
    [Inject] public LawFirmDataService LawFirmService { get; set; } = default!;
    private List<LawFirmDto> LawFirms { get; set; } = new();
    private SfToast? ToastObj;
    private List<LawyerDto> Lawyers { get; set; } = new();
    private List<LawyerDto> FilteredLawyers => _selectedRespondent.LawFirmId.HasValue
        ? Lawyers.Where(l => l.LawFirmId == _selectedRespondent.LawFirmId).ToList()
        : new List<LawyerDto>();

    private List<LawyerDto> FilteredPlaintiffLawyers => _selectedPlaintiff.LawFirmId.HasValue
        ? _lawyers.Where(l => l.LawFirmId == _selectedPlaintiff.LawFirmId).ToList()
        : new List<LawyerDto>();

    [Inject] private CourtDataService CourtService { get; set; } = default!;


    [Inject] private CaseVerdictService VerdictService { get; set; } = default!;
    [Inject] private CaseCategoryService CategoryService { get; set; } = default!;
    [Inject] private CaseEmployeeService EmployeeService { get; set; } = default!;
    //[Inject] private CouncilService CouncilService { get; set; } = default!;

    private CourtCaseDto CaseDto { get; set; } = new() { Plaintiffs = new List<PlaintiffDto>(), Respondents = new List<RespondentDto>(), CaseProceedings = new List<CaseProceedingDto>() };
    private List<CourtDto> Courts { get; set; } = new();
    private List<CaseCategoryDto> Categories { get; set; } = new();
    private List<CaseNatureDto> CaseNatures { get; set; } = new();

    [Inject] private CaseNatureService CaseNatureService { get; set; } = default!;
    [Inject] private AttachmentService AttachmentService { get; set; } = default!;
    [Inject] private CaseProceedingDataService ProceedingService { get; set; } = default!;
    [Inject] private IJSRuntime JSRuntime { get; set; } = default!;

    private List<CaseVerdictDto> Verdicts { get; set; } = new();
    private List<CaseEmployeeDto> _employees = new();
    private List<CouncilOfGroupCompanyDto> _groupCompanyCouncils = new();
    private List<CouncilOfNonGroupCompanyDto> _nonGroupCompanyCouncils = new();
    private List<PlaintiffTypeDto> _plaintiffTypes = new();
    private List<LawFirmDto> _lawFirms = new();
    private List<LawyerDto> _lawyers = new();

    private bool _isPrayDialogOpen;
    private bool _isSynopsisSelectorOpen;
    private bool _isVerdictSelectorOpen;
    private bool _isPlaintiffDialogOpen;
    private bool _isRespondentDialogOpen;
    private bool _isAttachmentDialogOpen;
    private bool _isProceedingDialogOpen;

    private PlaintiffDto _selectedPlaintiff = new();
    private RespondentDto _selectedRespondent = new();
    private AttachmentDto _selectedAttachment = new();

    // Attachment-related variables
    private List<AttachmentTypeDto> _attachmentTypes = new();
    private IBrowserFile? _selectedFile;
    private string _fileValidationMessage = string.Empty;
    private string _customFileName = string.Empty;

    // Proceeding-related variables
    private CaseProceedingDto _selectedProceeding = new();
    private List<ProceedingTypeDto> _proceedingTypes = new();
    private List<ProceedingStatusDto> _proceedingStatuses = new();
    private List<RoleInProceedingDto> _roleInProceedings = new();
    private List<OutcomeTypeDto> _outcomeTypes = new();


    protected override async Task OnParametersSetAsync()
    {
        await LoadReferenceData();
        if (Id.HasValue)
        {
            await LoadCase();
        }
    }

    private async Task LoadReferenceData()
    {
        Courts = await CourtService.GetCourtsAsync();
        Categories = await CategoryService.GetCategoriesAsync();
        CaseNatures = await CaseNatureService.GetCaseNaturesAsync();

        Verdicts = await VerdictService.GetVerdictsAsync();
        _employees = await EmployeeService.GetCaseEmployeesAsync();
        _groupCompanyCouncils = await CaseService.GetGroupCompanyCouncilsAsync();
        _nonGroupCompanyCouncils = await CaseService.GetNonGroupCompanyCouncilsAsync();
        LawFirms = await LawFirmService.GetLawFirmsAsync();
        Lawyers = await LawFirmService.GetLawyersAsync();
        _plaintiffTypes = await PlaintiffTypeService.GetPlaintiffTypesAsync();
        _lawFirms = await LawFirmService.GetLawFirmsAsync();
        _lawyers = await LawFirmService.GetLawyersAsync();
        _attachmentTypes = await AttachmentService.GetAttachmentTypesAsync();

        // Load proceeding-related data
        _proceedingTypes = await ProceedingService.GetProceedingTypesAsync();
        _proceedingStatuses = await ProceedingService.GetProceedingStatusesAsync();
        _roleInProceedings = await ProceedingService.GetRoleInProceedingsAsync();
        _outcomeTypes = await ProceedingService.GetOutcomeTypesAsync();
    }

    private async Task LoadCase()
    {
        if (Id.HasValue)
        {
            var loadedCase = await CaseService.GetCourtCaseByIdAsync(Id.Value);
            if (loadedCase != null)
            {
                CaseDto = loadedCase;
            }
        }
    }

    private async Task SaveCase()
    {
        var result = await CaseService.SaveCourtCaseAsync(CaseDto, "jawaid");
        if (result == "OK")
        {
            // display success message by using toastobj
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = "Case saved successfully",
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

        }
        else {
            // display error message
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
    }

    #region Pray Management
    private void OpenPraySelector() => _isPrayDialogOpen = true;




    #endregion

    #region Plaintiff Management
    private void OpenPlaintiffForm()
    {
        _selectedPlaintiff = new PlaintiffDto
        {
            CourtCaseId = Id ?? 0,
            Id = Guid.NewGuid() // Generate a new ID for the plaintiff
        };
        _isPlaintiffDialogOpen = true;
    }

    private void EditPlaintiff(PlaintiffDto plaintiff)
{
    _selectedPlaintiff = new PlaintiffDto
    {
        Id = plaintiff.Id,
        CourtCaseId = plaintiff.CourtCaseId,
        PlaintiffTypeId = plaintiff.PlaintiffTypeId,
        PlaintiffType = plaintiff.PlaintiffType,
        CaseEmployeeId = plaintiff.CaseEmployeeId,
        EmployeeCode = plaintiff.EmployeeCode,
        EmployeeName = plaintiff.EmployeeName,
        CNIC = plaintiff.CNIC,
        Department = plaintiff.Department,
        Designation = plaintiff.Designation,
        CouncilOfGroupCompanyId = plaintiff.CouncilOfGroupCompanyId,
        CouncilOfGroupCompanyName = plaintiff.CouncilOfGroupCompanyName,
        CouncilOfNonGroupCompanyId = plaintiff.CouncilOfNonGroupCompanyId,
        CouncilOfNonGroupCompanyName = plaintiff.CouncilOfNonGroupCompanyName,
        OtherPlaintiff = plaintiff.OtherPlaintiff,
        LawFirmId = plaintiff.LawFirmId,
        LawFirmName = plaintiff.LawFirmName,
        LawyerId = plaintiff.LawyerId,
        LawyerName = plaintiff.LawyerName
    };
    _isPlaintiffDialogOpen = true;
}

    private void SavePlaintiff()
    {
        // Set plaintiff type name
        var plaintiffType = _plaintiffTypes.FirstOrDefault(t => t.Id == _selectedPlaintiff.PlaintiffTypeId);
        if (plaintiffType != null)
        {
            _selectedPlaintiff.PlaintiffType = plaintiffType.Title;
        }

        // Set council names if applicable
        if (_selectedPlaintiff.CouncilOfGroupCompanyId.HasValue)
        {
            var groupCouncil = _groupCompanyCouncils.FirstOrDefault(c => c.Id == _selectedPlaintiff.CouncilOfGroupCompanyId);
            _selectedPlaintiff.CouncilOfGroupCompanyName = groupCouncil?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.CouncilOfGroupCompanyName = "";
        }

        if (_selectedPlaintiff.CouncilOfNonGroupCompanyId.HasValue)
        {
            var nonGroupCouncil = _nonGroupCompanyCouncils.FirstOrDefault(c => c.Id == _selectedPlaintiff.CouncilOfNonGroupCompanyId);
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = nonGroupCouncil?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = "";
        }

        // Set law firm and lawyer names if applicable
        if (_selectedPlaintiff.LawFirmId.HasValue)
        {
            var lawFirm = _lawFirms.FirstOrDefault(f => f.Id == _selectedPlaintiff.LawFirmId);
            _selectedPlaintiff.LawFirmName = lawFirm?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.LawFirmName = "";
        }

        if (_selectedPlaintiff.LawyerId.HasValue)
        {
            var lawyer = _lawyers.FirstOrDefault(l => l.Id == _selectedPlaintiff.LawyerId);
            _selectedPlaintiff.LawyerName = lawyer?.Name ?? "";
        }
        else
        {
            _selectedPlaintiff.LawyerName = "";
        }

        var existingPlaintiff = CaseDto.Plaintiffs.FirstOrDefault(p => p.Id == _selectedPlaintiff.Id);
        if (existingPlaintiff != null)
        {
            // Update existing plaintiff
            existingPlaintiff.PlaintiffTypeId = _selectedPlaintiff.PlaintiffTypeId;
            existingPlaintiff.PlaintiffType = _selectedPlaintiff.PlaintiffType;
            existingPlaintiff.CaseEmployeeId = _selectedPlaintiff.CaseEmployeeId;
            existingPlaintiff.EmployeeName = _selectedPlaintiff.EmployeeName;
            existingPlaintiff.EmployeeCode = _selectedPlaintiff.EmployeeCode;
            existingPlaintiff.CNIC = _selectedPlaintiff.CNIC;
            existingPlaintiff.Department = _selectedPlaintiff.Department;
            existingPlaintiff.Designation = _selectedPlaintiff.Designation;
            existingPlaintiff.CouncilOfGroupCompanyId = _selectedPlaintiff.CouncilOfGroupCompanyId;
            existingPlaintiff.CouncilOfGroupCompanyName = _selectedPlaintiff.CouncilOfGroupCompanyName;
            existingPlaintiff.CouncilOfNonGroupCompanyId = _selectedPlaintiff.CouncilOfNonGroupCompanyId;
            existingPlaintiff.CouncilOfNonGroupCompanyName = _selectedPlaintiff.CouncilOfNonGroupCompanyName;
            existingPlaintiff.OtherPlaintiff = _selectedPlaintiff.OtherPlaintiff;
            existingPlaintiff.LawFirmId = _selectedPlaintiff.LawFirmId;
            existingPlaintiff.LawFirmName = _selectedPlaintiff.LawFirmName;
            existingPlaintiff.LawyerId = _selectedPlaintiff.LawyerId;
            existingPlaintiff.LawyerName = _selectedPlaintiff.LawyerName;
        }
        else
        {
            // Add new plaintiff
            CaseDto.Plaintiffs.Add(_selectedPlaintiff);
        }

        // Sort plaintiffs by name or other appropriate field
        CaseDto.Plaintiffs = CaseDto.Plaintiffs.OrderBy(c =>
            c.PlaintiffTypeId == 1 ? c.EmployeeName :
            c.PlaintiffTypeId == 2 ? c.CouncilOfGroupCompanyName :
            c.PlaintiffTypeId == 3 ? c.CouncilOfNonGroupCompanyName :
            c.OtherPlaintiff).ToList();

        _isPlaintiffDialogOpen = false;
    }
    private void SavePlaintiff2()
    {
        // Set council names before saving
        if (_selectedPlaintiff.CouncilOfGroupCompanyId.HasValue)
        {
            var groupCouncil = _groupCompanyCouncils.FirstOrDefault(c => c.Id == _selectedPlaintiff.CouncilOfGroupCompanyId);
            _selectedPlaintiff.CouncilOfGroupCompanyName = groupCouncil?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.CouncilOfGroupCompanyName = "";
        }

        if (_selectedPlaintiff.CouncilOfNonGroupCompanyId.HasValue)
        {
            var nonGroupCouncil = _nonGroupCompanyCouncils.FirstOrDefault(c => c.Id == _selectedPlaintiff.CouncilOfNonGroupCompanyId);
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = nonGroupCouncil?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = "";
        }

        //if (_selectedPlaintiff.Id == Guid.Empty)  // Changed from 0 to Guid.Empty
        //{
        //    CaseDto.Plaintiffs.Add(_selectedPlaintiff);
        //}
        _isPlaintiffDialogOpen = false;
    }

    private void RemovePlaintiff(Guid Id)
    {
        //CaseDto.Plaintiffs.Remove(plaintiff);
        CaseDto.Plaintiffs = CaseDto.Plaintiffs.Where(c => c.Id != Id).ToList();

    }
    #endregion

    #region Synopsis Management
    private void OpenSynopsisSelector()
    {
        _isSynopsisSelectorOpen = true;
    }



    #endregion

    #region Verdict Management
    private void OpenVerdictSelector()
    {
        _isVerdictSelectorOpen = true;
    }

    private void RemoveVerdict()
    {
        CaseDto.CaseVerdictId = null;
        CaseDto.CaseVerdictTitle = "";
    }

    private void SelectVerdict(CaseVerdictDto verdict)
    {
        CaseDto.CaseVerdictId = verdict.Id;
        CaseDto.CaseVerdictTitle = verdict.Title;
        _isVerdictSelectorOpen = false;
    }
    #endregion

    #region Proceeding Management
    private void OpenProceedingDialog()
    {
        _selectedProceeding = new CaseProceedingDto
        {
            CourtCaseId = Id ?? 0,
            ProceedingDate = DateOnly.FromDateTime(DateTime.Today)
        };
        _isProceedingDialogOpen = true;
    }

    private void EditProceeding(CaseProceedingDto proceeding)
    {
        _selectedProceeding = new CaseProceedingDto
        {
            ProceedingId = proceeding.ProceedingId,
            CourtCaseId = proceeding.CourtCaseId,
            ProceedingDate = proceeding.ProceedingDate,
            ProceedingTime = proceeding.ProceedingTime,
            PresidingJudge = proceeding.PresidingJudge,
            CourtRoomNumber = proceeding.CourtRoomNumber,
            NextProceedingDate = proceeding.NextProceedingDate,
            NextProceedingTime = proceeding.NextProceedingTime,
            Remarks = proceeding.Remarks,
            ProceedingTypeId = proceeding.ProceedingTypeId,
            ProceedingStatusId = proceeding.ProceedingStatusId
        };
        _isProceedingDialogOpen = true;
    }

    private async Task SaveProceeding()
    {
        var result = await ProceedingService.SaveProceedingAsync(_selectedProceeding, "jawaid");
        if (result.Success)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

            // Reload case data to refresh proceedings
            await LoadCase();
            _isProceedingDialogOpen = false;
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
    }

    private async Task DeleteProceeding(int proceedingId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this proceeding? This will also delete all associated outcomes, attendees, and documents.");
        if (confirmed)
        {
            var result = await ProceedingService.DeleteProceedingAsync(proceedingId, "jawaid");
            if (result.Success)
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = result.Message,
                    CssClass = "e-success",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });

                // Reload case data to refresh proceedings
                await LoadCase();
            }
            else
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = result.Message,
                    CssClass = "e-error",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });
            }
        }
    }

    private void ManageOutcomes(int proceedingId)
    {
        // TODO: Implement outcome management dialog
        // This will open a dialog to manage outcomes for the specific proceeding
    }

    private void ManageAttendees(int proceedingId)
    {
        // TODO: Implement attendee management dialog
        // This will open a dialog to manage attendees for the specific proceeding
    }

    private void ManageDocuments(int proceedingId)
    {
        // TODO: Implement document management dialog
        // This will open a dialog to manage documents for the specific proceeding
    }
    #endregion

    private void OnPlaintiffTypeChanged(ChangeEventArgs<int, PlaintiffTypeDto> args)
    {
        _selectedPlaintiff.PlaintiffTypeId = args.Value;
        var selectedType = _plaintiffTypes.FirstOrDefault(t => t.Id == args.Value);
        if (selectedType != null)
        {
            _selectedPlaintiff.PlaintiffType = selectedType.Title;
        }

        // Reset fields based on plaintiff type
        if (_selectedPlaintiff.PlaintiffTypeId != 1)
        {
            _selectedPlaintiff.CaseEmployeeId = null;
            _selectedPlaintiff.EmployeeName = string.Empty;
            _selectedPlaintiff.EmployeeCode = string.Empty;
            _selectedPlaintiff.CNIC = string.Empty;
            _selectedPlaintiff.Department = string.Empty;
            _selectedPlaintiff.Designation = string.Empty;
        }

        if (_selectedPlaintiff.PlaintiffTypeId != 2)
        {
            _selectedPlaintiff.CouncilOfGroupCompanyId = null;
            _selectedPlaintiff.CouncilOfGroupCompanyName = string.Empty;
        }

        if (_selectedPlaintiff.PlaintiffTypeId != 3)
        {
            _selectedPlaintiff.CouncilOfNonGroupCompanyId = null;
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = string.Empty;
        }

        if (_selectedPlaintiff.PlaintiffTypeId != 4)
        {
            _selectedPlaintiff.OtherPlaintiff = string.Empty;
        }

        StateHasChanged();
    }

    private void OnLawFirmChanged(ChangeEventArgs<int?, LawFirmDto> args)
    {
        _selectedPlaintiff.LawFirmId = args.Value;
        _selectedPlaintiff.LawyerId = null;

        if (args.Value.HasValue)
        {
            var lawFirm = _lawFirms.FirstOrDefault(f => f.Id == args.Value);
            if (lawFirm != null)
            {
                _selectedPlaintiff.LawFirmName = lawFirm.Title;
            }
        }
        else
        {
            _selectedPlaintiff.LawFirmName = string.Empty;
        }

        StateHasChanged();
    }

    private void OnEmployeeSelectionChanged(ChangeEventArgs<int?, CaseEmployeeDto> args)
    {
        if (args.Value.HasValue)
        {
            var selectedEmployee = _employees.FirstOrDefault(e => e.Id == args.Value);
            if (selectedEmployee != null)
            {
                _selectedPlaintiff.EmployeeName = selectedEmployee.Name;
                _selectedPlaintiff.EmployeeCode = selectedEmployee.EmployeeCode;
                _selectedPlaintiff.CNIC = selectedEmployee.CNIC;
                _selectedPlaintiff.Department = selectedEmployee.Department;
                _selectedPlaintiff.Designation = selectedEmployee.Designation;
            }
        }
        else
        {
            _selectedPlaintiff.EmployeeName = string.Empty;
            _selectedPlaintiff.EmployeeCode = string.Empty;
            _selectedPlaintiff.CNIC = string.Empty;
            _selectedPlaintiff.Department = string.Empty;
            _selectedPlaintiff.Designation = string.Empty;
        }
        StateHasChanged();
    }

    //private async Task OnGroupCouncilChange(ChangeEventArgs<int?, CouncilOfGroupCompanyDto> args)
    //{
    //    if (args.Value.HasValue)
    //    {
    //        var council = _groupCompanyCouncils.FirstOrDefault(c => c.Id == args.Value);
    //        if (council != null)
    //        {
    //            _selectedPlaintiff.CouncilOfGroupCompanyName = council.Name;
    //        }
    //    }
    //    else
    //    {
    //        _selectedPlaintiff.CouncilOfGroupCompanyName = string.Empty;
    //    }
    //}

    //private async Task OnNonGroupCouncilChange(ChangeEventArgs<int?, CouncilOfNonGroupCompanyDto> args)
    //{
    //    if (args.Value.HasValue)
    //    {
    //        var council = _nonGroupCompanyCouncils.FirstOrDefault(c => c. == args.Value);
    //        if (council != null)
    //        {
    //            _selectedPlaintiff.CouncilOfNonGroupCompanyName = council.Name;
    //        }
    //    }
    //    else
    //    {
    //        _selectedPlaintiff.CouncilOfNonGroupCompanyName = string.Empty;
    //    }
    //}


    public void EditRespondent(RespondentDto resp)
    {
        _selectedRespondent = resp;
        _isRespondentDialogOpen = true;
    }

    public void RemoveRespondent(RespondentDto resp)
    {
        //CaseDto.Respondents?.Remove(resp);
        CaseDto.Respondents = CaseDto.Respondents.Where(c => c.Id != resp.Id).ToList();

    }

    public void SaveRespondent()
    {
        if (_selectedRespondent.LawFirmId.HasValue)
        {
            var lawFirm = LawFirms.First(f => f.Id == _selectedRespondent.LawFirmId);
            _selectedRespondent.LawFirmName = lawFirm.Title;
        }
        else
        {
            _selectedRespondent.LawFirmName = "";
        }

        if (_selectedRespondent.LawyerId.HasValue)
        {
            var lawyer = Lawyers.FirstOrDefault(l => l.Id == _selectedRespondent.LawyerId);
            if (lawyer == null)
                _selectedRespondent.LawyerName = "";
            else
            {
                _selectedRespondent.LawyerName = lawyer.Name;
            }

        }
        else
        {
            _selectedRespondent.LawyerName = "";
        }

        var rs = CaseDto.Respondents.FirstOrDefault(c => c.Id == _selectedRespondent.Id);
        if (rs != null)
        {
            rs.LawyerName = _selectedRespondent.LawyerName;
            rs.LawyerId = _selectedRespondent.LawyerId;
            rs.LawFirmId = _selectedRespondent.LawFirmId;
            rs.LawFirmName = _selectedRespondent.LawFirmName;
            rs.Name = _selectedRespondent.Name;
        }
        else
        {
            rs = new RespondentDto
            {
                LawFirmName = _selectedRespondent.LawFirmName,
                LawFirmId = _selectedRespondent.LawFirmId,
                Id = _selectedRespondent.Id,
                LawyerId = _selectedRespondent.LawyerId,
                LawyerName = _selectedRespondent.LawyerName,
                Name = _selectedRespondent.Name
            };

            CaseDto.Respondents.Add(rs);
            CaseDto.Respondents = CaseDto.Respondents.OrderBy(c => c.Name).ToList();

        }
        _isRespondentDialogOpen = false;
    }

    public void SaveRespondent2()
    {
        // Set the law firm name and lawyer name before saving
        if (_selectedRespondent.LawFirmId.HasValue)
        {
            var lawFirm = LawFirms.FirstOrDefault(f => f.Id == _selectedRespondent.LawFirmId);
            _selectedRespondent.LawFirmName = lawFirm?.Title ?? "";
        }
        else
        {
            _selectedRespondent.LawFirmName = "";
        }

        if (_selectedRespondent.LawyerId.HasValue)
        {
            var lawyer = Lawyers.FirstOrDefault(l => l.Id == _selectedRespondent.LawyerId);
            _selectedRespondent.LawyerName = lawyer?.Name ?? "";
        }
        else
        {
            _selectedRespondent.LawyerName = "";
        }

        if (CaseDto.Respondents == null)
        {
            CaseDto.Respondents = new List<RespondentDto>();
        }

        var existingRespondent = CaseDto.Respondents.FirstOrDefault(r => r.Id == _selectedRespondent.Id);
        if (existingRespondent != null)
        {
            // Update existing respondent
            var index = CaseDto.Respondents.IndexOf(existingRespondent);
            CaseDto.Respondents[index] = _selectedRespondent;
        }
        else
        {
            // Add new respondent
            CaseDto.Respondents.Add(_selectedRespondent);
        }

        _isRespondentDialogOpen = false;
    }

    //private void onlawfirmchange(int? lawfirmid)
    //{
    //    _selectedrespondent.lawfirmid = lawfirmid;
    //    _selectedrespondent.lawyerid = null; // reset lawyer selection when law firm changes
    //    statehaschanged();
    //}

    #region Attachment Management
    private void OpenAttachmentDialog()
    {
        _selectedAttachment = new AttachmentDto
        {
            CourtCaseId = Id ?? 0
        };
        _selectedFile = null;
        _fileValidationMessage = string.Empty;
        _customFileName = string.Empty;
        _isAttachmentDialogOpen = true;
    }

    private void OnFileSelected(InputFileChangeEventArgs e)
    {
        _selectedFile = e.File;
        _fileValidationMessage = string.Empty;

        // Validate file
        if (_selectedFile != null)
        {
            var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
            var extension = Path.GetExtension(_selectedFile.Name).ToLowerInvariant();

            if (!allowedExtensions.Contains(extension))
            {
                _fileValidationMessage = $"File type not allowed. Allowed types: {string.Join(", ", allowedExtensions)}";
                _selectedFile = null;
                return;
            }

            if (_selectedFile.Size > 10 * 1024 * 1024) // 10MB
            {
                _fileValidationMessage = "File size cannot exceed 10 MB";
                _selectedFile = null;
                return;
            }
        }
    }

    private void ClearSelectedFile()
    {
        _selectedFile = null;
        _fileValidationMessage = string.Empty;
    }

    private async Task SaveAttachment()
    {
        if (_selectedFile == null || _selectedAttachment.AttachmentTypeId == null)
        {
            return;
        }

        try
        {
            var result = await AttachmentService.SaveAttachmentAsync(
                _selectedFile,
                _customFileName,
                Id ?? 0,
                _selectedAttachment.AttachmentTypeId.Value,
                "jawaid"); // TODO: Get actual user ID

            if (result.Success)
            {
                // Reload attachments
                if (Id.HasValue && CaseDto != null)
                {
                    CaseDto.Attachments = await AttachmentService.GetAttachmentsByCourtCaseIdAsync(Id.Value);
                }

                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = result.Message,
                    CssClass = "e-success",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });

                _isAttachmentDialogOpen = false;
            }
            else
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = result.Message,
                    CssClass = "e-error",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });
            }
        }
        catch (Exception ex)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = $"Error uploading file: {ex.Message}",
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
    }

    private async Task DownloadAttachment(AttachmentDto attachment)
    {
        await JSRuntime.InvokeVoidAsync("open", attachment.FileUrl, "_blank");
    }

    private async Task DeleteAttachment(AttachmentDto attachment)
    {
        try
        {
            var result = await AttachmentService.DeleteAttachmentAsync(attachment.Id, "jawaid");

            if (result.Success)
            {
                // Remove from list
                CaseDto?.Attachments.Remove(attachment);

                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = result.Message,
                    CssClass = "e-success",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });
            }
            else
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = result.Message,
                    CssClass = "e-error",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });
            }
        }
        catch (Exception ex)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = $"Error deleting attachment: {ex.Message}",
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
    }

    private static string FormatFileSize(long bytes)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = { "B", "KB", "MB", "GB" };
        int order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }
    #endregion
}

