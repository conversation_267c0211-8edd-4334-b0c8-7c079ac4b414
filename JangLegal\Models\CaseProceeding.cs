﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace JangLegal.Models;

public partial class CaseProceeding
{
    public int ProceedingId { get; set; }

    public int CourtCaseId { get; set; }

    public DateOnly ProceedingDate { get; set; }

    public TimeOnly? ProceedingTime { get; set; }

    public string PresidingJudge { get; set; }

    public string CourtRoomNumber { get; set; }

    public DateOnly? NextProceedingDate { get; set; }

    public TimeOnly? NextProceedingTime { get; set; }

    public string Remarks { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int? ProceedingTypeId { get; set; }

    public int? ProceedingStatusId { get; set; }

    public virtual CourtCase CourtCase { get; set; }

    public virtual ICollection<ProceedingAttendee> ProceedingAttendees { get; set; } = new List<ProceedingAttendee>();

    public virtual ICollection<ProceedingDocument> ProceedingDocuments { get; set; } = new List<ProceedingDocument>();

    public virtual ICollection<ProceedingOutcome> ProceedingOutcomes { get; set; } = new List<ProceedingOutcome>();

    public virtual ProceedingStatus ProceedingStatus { get; set; }

    public virtual ProceedingType ProceedingType { get; set; }
}