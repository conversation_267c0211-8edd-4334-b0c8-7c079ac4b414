using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class CourtCaseService
{
    private readonly IDbContextFactory<ApplicationDbContext> dcFactory;

    public CourtCaseService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
    {
        dcFactory = dbContextFactory;
    }
    public Task<List<CouncilOfGroupCompanyDto>> GetGroupCompanyCouncilsAsync()
    {
        using var dc =  dcFactory.CreateDbContext();
        var q = (from a in dc.CouncilOfGroupCompanies
                 orderby a.Title
                 select new CouncilOfGroupCompanyDto
                 {
                     Id = a.Id,
                     Title = a.Title
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<CouncilOfNonGroupCompanyDto>> GetNonGroupCompanyCouncilsAsync()
    {
        using var dc =  dcFactory.CreateDbContext();
        var q = (from a in dc.CouncilOfNonGroupCompanies
                 orderby a.Title
                 select new CouncilOfNonGroupCompanyDto
                 {
                     Id = a.Id,
                     Title = a.Title
                 }).ToList();
        return Task.FromResult(q);
    }

// GetCaseNaturesAsync
public Task<List<CaseNatureDto>> GetCaseNaturesAsync()
{
    using var dc =  dcFactory.CreateDbContext();
    var q = (from a in dc.CaseNatures
             orderby a.Name
             select new CaseNatureDto
             {
                 Id = a.Id,
                 Name = a.Name
             }).ToList();
    return Task.FromResult(q);
}
    public Task<List<CaseCategoryDto>> GetCategoriesAsync()
    {
        using var dc =  dcFactory.CreateDbContext();
        var q = (from a in dc.CaseCategories
                 orderby a.Title
                 select new CaseCategoryDto
                 {
                     Id = a.Id,
                     Title = a.Title
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<CaseEmployeeDto>> GetCaseEmployeesAsync()
    {
        using var dc = dcFactory.CreateDbContext();

        var q = (from a in dc.CaseEmployees
                 orderby a.Name.Trim()
                 select new CaseEmployeeDto
                 {
                     Id = a.Id,
                     Name = $"{a.EmployeeCode} | {a.Name} | {a.Cnic} - ({a.Department} - {a.Department})"
                 }).ToList();

        return Task.FromResult(q);
    }
    public async Task<List<CourtCaseDto>> GetCourtCasesAsync(string[]? employeeIds = null, string[]? courtIds = null, string[]? categoryIds = null, string[]? caseNatureId = null, bool? isDecided = null)
    {
        using var dc = dcFactory.CreateDbContext();
        try
        {
            // Start with base query
            var query = dc.CourtCases
                .AsNoTracking()
                .Where(c => !c.IsDeleted);

            // Apply filters if provided
            if (employeeIds?.Length > 0)
            {
                query = query.Where(c => c.Plaintiffs.Any(p => employeeIds.Contains(p.CaseEmployeeId.ToString())));
            }

            if (courtIds?.Length > 0)
            {
                query = query.Where(c => courtIds.Contains(c.CourtId.ToString()));
            }

            if (categoryIds?.Length > 0)
            {
                query = query.Where(c => categoryIds.Contains(c.CaseCategoryId.ToString()));
            }
            if (caseNatureId?.Length> 0)
            {
                query = query.Where(c => caseNatureId.Contains(c.CaseNatureId.ToString()));
            }
            if (isDecided.HasValue)
            {
                query = query.Where(c => c.IsDecided == isDecided.Value);
            }

            // Include related data and project to DTO
            return await query
                .Include(c => c.Court)
                .Include(c => c.CaseVerdict)
                .Include(c => c.CaseCategory)
                .Include(c=>c.CaseNature)
                .Include(c => c.Plaintiffs)
                    .ThenInclude(p => p.CaseEmployee)
                .Include(c => c.Plaintiffs)
                    .ThenInclude(p => p.CouncilOfGroupCompany)
                .Include(c => c.Plaintiffs)
                    .ThenInclude(p => p.CouncilOfNonGroupCompany)
                .Include(c => c.Respondents)
                    .ThenInclude(r => r.LawFirm)
                .Include(c => c.Respondents)
                    .ThenInclude(r => r.Lawyer)
                .Select(c => new CourtCaseDto
                {
                    Id = c.Id,
                    CaseNumber = c.CaseNumber,
                    Title = c.Title,
                    CourtId = c.CourtId,
                    CourtName = c.Court.Name,
                    CaseFilingYear = c.CaseFilingYear,
                    Pray = c.Pray,
                    CaseSynopsis = c.CaseSynopsis,
                    CaseVerdictId = c.CaseVerdictId,
                    CaseVerdictTitle = c.CaseVerdict.Title,
                    DateInOffice = c.DateInOffice,
                    ClaimAmount = c.ClaimAmount,
                    CaseCategoryId = c.CaseCategoryId,
                    CategoryTitle = c.CaseCategory.Title,
                    StayOrderId = c.StayOrderId,
                    RespondentCount = c.Respondents.Count(r => !r.IsDeleted),
                    StayOrderTitle = c.StayOrder.Text,
                    PlaintiffCount = c.Plaintiffs.Count(),
                    CaseNature = c.CaseNature != null ? c.CaseNature.Name : "",
                    IsDecided = c.IsDecided,

                })
                .AsSplitQuery()
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in GetCourtCasesAsync: {ex.Message}");
            return new List<CourtCaseDto>();
        }
    }

    public async Task<string> SaveCourtCaseAsync(CourtCaseDto dto, string userId)
    {
        using var dc =  dcFactory.CreateDbContext();
        //return Task.FromResult("OK");
        try
        {
            if (dto.Id == 0)
            {
                var courtCase = new CourtCase
                {
                    CaseNumber = dto.CaseNumber,
                    Title = dto.Title,
                    CourtId = dto.CourtId,
                    CaseFilingYear = dto.CaseFilingYear,
                    Pray = dto.Pray,
                    CaseSynopsis = dto.CaseSynopsis,
                    CaseVerdictId = dto.CaseVerdictId,
                    DateInOffice = dto.DateInOffice,
                    ClaimAmount = dto.ClaimAmount,
                    CaseCategoryId = dto.CaseCategoryId,
                    StayOrderId = dto.StayOrderId,
                    CreatedBy = userId,
                    CreatedDate = DateTime.Now,
                    ModifiedBy = userId,
                    ModifiedDate = DateTime.Now,
                    IsDecided = dto.IsDecided,
                };

                dc.CourtCases.Add(courtCase);
                await dc.SaveChangesAsync();

            }
            else
            {
                var courtCase = await dc.CourtCases.FindAsync(dto.Id);
                if (courtCase == null)
                    return "Court case not found";

                courtCase.CaseNumber = dto.CaseNumber;
                courtCase.Title = dto.Title;
                courtCase.CourtId = dto.CourtId;
                courtCase.CaseFilingYear = dto.CaseFilingYear;
                courtCase.Pray = dto.Pray;
                courtCase.CaseSynopsis = dto.CaseSynopsis;
                courtCase.CaseVerdictId = dto.CaseVerdictId;
                courtCase.DateInOffice = dto.DateInOffice;
                courtCase.ClaimAmount = dto.ClaimAmount;
                courtCase.CaseCategoryId = dto.CaseCategoryId;
                courtCase.StayOrderId = dto.StayOrderId;
                courtCase.ModifiedBy = userId;
                courtCase.ModifiedDate = DateTime.Now;
                courtCase.IsDecided = dto.IsDecided;
                await dc.SaveChangesAsync();

            }

            var plf = (from a in dc.Plaintiffs
                       where a.CourtCaseId==dto.Id
                       select a).ToList();

            // remove all plaintiffs
            dc.Plaintiffs.RemoveRange(plf);
            dc.SaveChanges();

            foreach (var pp in dto.Plaintiffs)
            {
                var m = new Plaintiff
                {
                    CourtCaseId = dto.Id,
                    CaseEmployeeId = pp.CaseEmployeeId,
                    CouncilOfGroupCompanyId = pp.CouncilOfGroupCompanyId,
                    CouncilOfNonGroupCompanyId = pp.CouncilOfNonGroupCompanyId,
                    CreatedBy = userId,
                    CreatedDate = DateTime.Now,
                    ModifiedBy = userId,
                    ModifiedDate = DateTime.Now
                };
                dc.Plaintiffs.Add(m);
            }

            // remove all respondents
            var rsp = (from a in dc.Respondents
                       where a.CourtCaseId == dto.Id
                       select a).ToList();
            dc.Respondents.RemoveRange(rsp);
            // re-insert respondents
            foreach (var pp in dto.Respondents)
            {
                var m = new Respondent
                {
                    CourtCaseId = dto.Id,
                    RespondentName = pp.Name,
                    LawFirmId = pp.LawFirmId,
                    LawyerId = pp.LawyerId,
                    CreatedBy = userId,
                    CreatedDate = DateTime.Now,
                    ModifiedBy = userId,
                    ModifiedDate = DateTime.Now
                };
                dc.Respondents.Add(m);
            }

            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public async Task<string> DeleteCourtCaseAsync(int id, string userId)
    {
        using var dc =  dcFactory.CreateDbContext();
        try
        {
            var courtCase = await dc.CourtCases.FindAsync(id);
            if (courtCase == null)
                return "Court case not found";

            courtCase.IsDeleted = true;
            courtCase.ModifiedBy = userId;
            courtCase.ModifiedDate = DateTime.Now;

            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public Task<CourtCaseDto?> GetCourtCaseByIdAsync(int id)
    {
        using var dc =  dcFactory.CreateDbContext();
        var q = (from a in dc.CourtCases
                 where a.Id == id
                 select new CourtCaseDto
                 {
                     CaseCategoryId = a.CaseCategoryId,
                     Id = id,
                     CaseFilingYear = a.CaseFilingYear,
                     CaseNumber = a.CaseNumber,
                     CaseSynopsis = a.CaseSynopsis,
                     Title = a.Title,
                     CaseVerdictId = a.CaseVerdictId,
                     CaseVerdictTitle = a.CaseVerdict!.Title,
                     CategoryTitle = a.CaseCategory.Title,
                     ClaimAmount = a.ClaimAmount,
                     CourtId = a.CourtId,
                     CourtName = a.Court.Name,
                     IsDecided = a.IsDecided,
                     DateInOffice = a.DateInOffice,

                     Pray = a.Pray,
                     StayOrderId = a.StayOrderId,
                     StayOrderTitle = a.StayOrder.Text,
                     Plaintiffs = (from pp in dc.Plaintiffs
                                   where pp.CourtCaseId == id
                                   select new PlaintiffDto
                                   {
                                       CourtCaseId = pp.CourtCaseId,
                                       CaseEmployeeId = pp.CaseEmployeeId,
                                       CouncilOfGroupCompanyId = pp.CouncilOfNonGroupCompanyId,
                                       CouncilOfGroupCompanyName = pp.CouncilOfNonGroupCompany.Title,
                                       CouncilOfNonGroupCompanyId = pp.CouncilOfNonGroupCompanyId,
                                       CouncilOfNonGroupCompanyName = pp.CouncilOfNonGroupCompany.Title,
                                       CreatedBy = pp.CreatedBy,
                                       CreatedDate = pp.CreatedDate,
                                       EmployeeCode = pp.CaseEmployee.EmployeeCode,
                                       EmployeeName = pp.CaseEmployee.Name,
                                       Id = pp.Id,
                                       IsDeleted = false,
                                       ModifiedBy = pp.ModifiedBy,
                                       ModifiedDate = pp.ModifiedDate,
                                       CNIC = pp.CaseEmployee.Cnic,
                                       Department =pp.CaseEmployee.Department,
                                       Designation = pp.CaseEmployee.Designation,
                                       PlaintiffTypeId = pp.TypeId ?? 1,
                                       PlaintiffType = pp.TypeId.HasValue ? pp.Type.Title : "Employee"

                                   }).ToList(),
                     Respondents = (from rr in dc.Respondents
                                    where rr.CourtCaseId == id
                                    select new RespondentDto
                                    {
                                        CourtCaseId = rr.CourtCaseId,
                                        ModifiedDate = rr.ModifiedDate,
                                        CreatedBy = rr.CreatedBy,
                                        CreatedDate = rr.CreatedDate,
                                        Id = rr.Id,
                                        IsDeleted = rr.IsDeleted,
                                        LawFirmId = rr.LawFirmId,
                                        LawFirmName = rr.LawFirm.Title,
                                        LawyerId = rr.LawyerId,
                                        LawyerName = rr.Lawyer.Name,
                                        Name = rr.RespondentName,
                                        ModifiedBy = rr.ModifiedBy,
                                    }).ToList(),
                     Attachments = (from att in dc.Attachments
                                   where att.CourtCaseId == id
                                   select new AttachmentDto
                                   {
                                       Id = att.Id,
                                       CourtCaseId = att.CourtCaseId,
                                       FileName = att.FileName,
                                       
                                       FilePhysicalPath = att.FilePhysicalPath,
                                       FileUrl = att.FileUrl,
                                       CreatedDate = att.CreatedDate,
                                       CreatedBy = att.CreatedBy,
                                       ModifiedBy = att.ModifiedBy,
                                       ModifiedDate = att.ModifiedDate,
                                       AttachmentTypeId = att.AttachmentTypeId,
                                       AttachmentTypeName = att.AttachmentType != null ? att.AttachmentType.Title : ""
                                   }).OrderByDescending(att => att.CreatedDate).ToList(),
                     CaseProceedings = (from cp in dc.CaseProceedings
                                       where cp.CourtCaseId == id
                                       orderby cp.ProceedingDate
                                       select new CaseProceedingDto
                                       {
                                           ProceedingId = cp.ProceedingId,
                                           CourtCaseId = cp.CourtCaseId,
                                           ProceedingDate = cp.ProceedingDate,
                                           PresidingJudge = cp.PresidingJudge ?? "",
                                           CourtRoomNumber = cp.CourtRoomNumber ?? "",
                                           NextProceedingDate = cp.NextProceedingDate,
                                           Remarks = cp.Remarks ?? "",
                                           CreatedDate = cp.CreatedDate,
                                           ModifiedDate = cp.ModifiedDate,
                                           ProceedingTypeId = cp.ProceedingTypeId,
                                           ProceedingTypeName = cp.ProceedingType != null ? cp.ProceedingType.TypeName : "",
                                           ProceedingStatusId = cp.ProceedingStatusId,
                                           ProceedingStatusName = cp.ProceedingStatus != null ? cp.ProceedingStatus.StatusName : ""
                                       }).ToList()

                 }).FirstOrDefault();
        return Task.FromResult(q);
    }
}
